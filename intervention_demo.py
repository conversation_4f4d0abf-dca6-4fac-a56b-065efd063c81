#!/usr/bin/env python3
"""
Demonstration of the fixed test_intervention function.

This script shows how to properly use the test_intervention function
and access its results after the fixes.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from itas.analysis.representation_engineer import InterventionResult

def demo_intervention_results():
    """Demonstrate how to use InterventionResult properly."""
    print("🎯 Demonstration: Using test_intervention results")
    print("=" * 60)
    
    # Simulate what test_intervention returns after our fixes
    print("\n1️⃣ Creating sample InterventionResult (as returned by test_intervention)...")
    
    # This is what the fixed test_intervention function returns
    sample_results = [
        {
            "input": "What is the capital of France?",
            "original": "What is the capital of France? The capital of France is Paris.",
            "modified": "What is the capital of France? The beautiful capital of France is Paris.",
        },
        {
            "input": "How does photosynthesis work?",
            "original": "How does photosynthesis work? Photosynthesis is the process by which plants convert light energy into chemical energy.",
            "modified": "How does photosynthesis work? Photosynthesis is the amazing process by which plants convert light energy into chemical energy.",
        }
    ]
    
    # Separate original and modified outputs (new structure)
    original_outputs = [
        {
            "input": "What is the capital of France?",
            "output": "What is the capital of France? The capital of France is Paris.",
            "tokens": [1, 2, 3, 4, 5, 6, 7, 8, 9],
        },
        {
            "input": "How does photosynthesis work?",
            "output": "How does photosynthesis work? Photosynthesis is the process by which plants convert light energy into chemical energy.",
            "tokens": [10, 11, 12, 13, 14, 15, 16, 17, 18],
        }
    ]
    
    modified_outputs = [
        {
            "input": "What is the capital of France?",
            "output": "What is the capital of France? The beautiful capital of France is Paris.",
            "tokens": [1, 2, 3, 4, 19, 5, 6, 7, 8, 9],
        },
        {
            "input": "How does photosynthesis work?",
            "output": "How does photosynthesis work? Photosynthesis is the amazing process by which plants convert light energy into chemical energy.",
            "tokens": [10, 11, 12, 13, 14, 15, 20, 16, 17, 18],
        }
    ]
    
    # Create the InterventionResult as test_intervention would
    results = InterventionResult(
        original_output=sample_results,  # Combined results for backward compatibility
        modified_output=modified_outputs,  # Separate modified outputs
        intervention_strength=1.5,
        affected_features=[42, 123, 456],
        metadata={
            "test_texts": ["What is the capital of France?", "How does photosynthesis work?"],
            "generation_kwargs": {"max_new_tokens": 50, "temperature": 0.7},
            "original_outputs": original_outputs,  # Separate original outputs
        }
    )
    
    print("✅ InterventionResult created!")
    
    # Show different ways to access the results
    print("\n2️⃣ Method 1: Tutorial-style iteration (RECOMMENDED)")
    print("-" * 50)
    
    test_prompts = ["What is the capital of France?", "How does photosynthesis work?"]
    
    # This is how the tutorial code should work now
    for i, (prompt, result) in enumerate(zip(test_prompts, results)):
        print(f"\nPrompt {i+1}: {prompt}")
        
        # Check if this is an activation comparison result
        if results.is_activation_comparison():
            # For activation comparison results
            print(f"  Original: {result['original']}")
            print(f"  Modified: {result['modified']}")
            if 'activation_diff_norm' in result:
                print(f"  Activation difference: {result['activation_diff_norm']:.4f}")
            if 'cosine_similarity' in result:
                print(f"  Cosine similarity: {result['cosine_similarity']:.4f}")
        else:
            # For text generation results
            print(f"  Original: {result['original'][:60]}...")
            print(f"  Modified: {result['modified'][:60]}...")
    
    print("\n3️⃣ Method 2: Using helper methods")
    print("-" * 50)
    
    # Access combined results (backward compatibility)
    combined_results = results.get_combined_results()
    print(f"Combined results: {len(combined_results)} items")
    
    # Access separate original outputs
    original_outputs_retrieved = results.get_original_outputs()
    print(f"Original outputs: {len(original_outputs_retrieved)} items")
    for i, orig in enumerate(original_outputs_retrieved):
        print(f"  Original {i+1}: {orig['output'][:40]}...")
    
    # Access separate modified outputs
    modified_outputs_retrieved = results.get_modified_outputs()
    print(f"Modified outputs: {len(modified_outputs_retrieved)} items")
    for i, mod in enumerate(modified_outputs_retrieved):
        print(f"  Modified {i+1}: {mod['output'][:40]}...")
    
    print("\n4️⃣ Method 3: Direct iteration over results")
    print("-" * 50)
    
    for i, result in enumerate(results):
        print(f"Result {i+1}:")
        print(f"  Input: {result['input']}")
        print(f"  Original: {result['original'][:40]}...")
        print(f"  Modified: {result['modified'][:40]}...")
    
    print("\n5️⃣ Method 4: Accessing metadata")
    print("-" * 50)
    
    print(f"Intervention strength: {results.intervention_strength}")
    print(f"Affected features: {results.affected_features}")
    print(f"Test texts: {results.metadata['test_texts']}")
    print(f"Generation kwargs: {results.metadata['generation_kwargs']}")
    print(f"Is activation comparison: {results.is_activation_comparison()}")

def demo_activation_comparison_results():
    """Demonstrate activation comparison results."""
    print("\n\n🔬 Demonstration: Activation comparison results")
    print("=" * 60)
    
    # Simulate activation comparison results
    activation_results = [
        {
            "input": "Hello, how are you?",
            "original": "Original activations (norm: 12.3456)",
            "modified": "Modified activations (norm: 12.4567)",
            "activation_diff_norm": 0.1234,
            "cosine_similarity": 0.9876,
        }
    ]
    
    original_outputs = [
        {
            "input": "Hello, how are you?",
            "output": "Original activations (norm: 12.3456)",
            "norm": 12.3456,
        }
    ]
    
    modified_outputs = [
        {
            "input": "Hello, how are you?",
            "output": "Modified activations (norm: 12.4567)",
            "norm": 12.4567,
            "activation_diff_norm": 0.1234,
            "cosine_similarity": 0.9876,
        }
    ]
    
    results = InterventionResult(
        original_output=activation_results,
        modified_output=modified_outputs,
        intervention_strength=1.0,
        affected_features=[],
        metadata={
            "test_texts": ["Hello, how are you?"],
            "method": "activation_comparison",
            "note": "Model does not support generation - using activation comparison",
            "original_outputs": original_outputs,
        }
    )
    
    print("✅ Activation comparison InterventionResult created!")
    print(f"Is activation comparison: {results.is_activation_comparison()}")
    
    # Handle activation comparison results
    test_prompts = ["Hello, how are you?"]
    
    for i, (prompt, result) in enumerate(zip(test_prompts, results)):
        print(f"\nPrompt {i+1}: {prompt}")
        
        if results.is_activation_comparison():
            print(f"  Original: {result['original']}")
            print(f"  Modified: {result['modified']}")
            print(f"  Activation difference norm: {result['activation_diff_norm']:.4f}")
            print(f"  Cosine similarity: {result['cosine_similarity']:.4f}")
        else:
            print(f"  Original: {result['original'][:60]}...")
            print(f"  Modified: {result['modified'][:60]}...")

if __name__ == "__main__":
    print("🚀 InterventionResult Usage Demonstration")
    print("This shows how to properly use the fixed test_intervention function results.\n")
    
    demo_intervention_results()
    demo_activation_comparison_results()
    
    print("\n\n🎉 Demonstration complete!")
    print("\nKey improvements in the fixed test_intervention function:")
    print("✅ Proper separation of original and modified outputs")
    print("✅ Backward compatibility with existing tutorial code")
    print("✅ Helper methods for easy access to different result types")
    print("✅ Consistent structure for both generation and activation comparison")
    print("✅ Proper iteration support for tutorial-style usage")
