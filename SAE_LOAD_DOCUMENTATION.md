# SAE Load Function Documentation

## Overview

The `SAE.load()` class method provides a unified interface for loading Sparse Auto-Encoders (SAEs) from multiple sources:

1. **Local PyTorch files** (`.pt`, `.pth`, `.safetensors`)
2. **Hugging Face Hub repositories**

## Basic Usage

### Loading from Local Files

```python
from itas.core.sae import SAE

# Load from a local .pt file
sae = SAE.load("path/to/model.pt")

# Load with specific device and dtype
sae = SAE.load("model.pt", device="cuda", dtype=torch.float16)
```

### Loading from Hugging Face Hub

```python
# Load from a HuggingFace repository
sae = SAE.load("username/sae-repo-name")

# Load from a specific folder within the repo (e.g., EleutherAI format)
sae = SAE.load("EleutherAI/sae-llama-3.1-8b-32x", folder_name="layers.23.mlp")

# Force re-download
sae = SAE.load("username/repo", force_download=True)
```

## Method Signature

```python
@classmethod
def load(
    cls,
    path: Union[str, Path],
    device: Union[str, torch.device] = "cpu",
    dtype: Optional[torch.dtype] = None,
    folder_name: Optional[str] = None,
    force_download: bool = False,
    **kwargs
) -> "SAE":
```

### Parameters

- **`path`**: Either a Hugging Face repo ID (e.g., "username/repo-name") or local file path
- **`device`**: Device to load the model on (default: "cpu")
- **`dtype`**: Data type for model parameters (default: inferred from file)
- **`folder_name`**: For HF repos, specific folder containing SAE files (default: root)
- **`force_download`**: Force re-download from HF Hub (default: False)
- **`**kwargs`**: Additional arguments for model initialization

## Supported File Formats

### Local Files

The loader supports multiple checkpoint formats:

1. **Standard format**:
   ```python
   {
       'config': {...},
       'state_dict': {...}
   }
   ```

2. **ITAS format**:
   ```python
   {
       'config': {...},
       'sae_state_dict': {...}
   }
   ```

3. **Alternative format**:
   ```python
   {
       'model_config': {...},
       'model_state_dict': {...}
   }
   ```

4. **Direct state dict**: Just the model weights without separate config

5. **SafeTensors format**: `.safetensors` files with automatic detection

### Hugging Face Hub

For HF repositories, the loader expects:
- A configuration file: `config.json`, `sae_config.json`, or `cfg.json` (EleutherAI format)
- A weights file: `.safetensors` or `.pt` file (e.g., `sae.safetensors` for EleutherAI)

## Automatic Configuration Inference

When configuration is missing or incomplete, the loader can automatically infer:

- **Dimensions** (`d_in`, `d_sae`) from weight matrix shapes
- **Architecture** (standard/gated/jumprelu) from available parameters
- **Decoder bias** presence from state dict keys

## Error Handling

The loader provides clear error messages for common issues:

- **File not found**: `FileNotFoundError` for missing local files
- **Repository not found**: `ValueError` for invalid HF repos
- **Missing dependencies**: `ImportError` for missing packages
- **Invalid format**: `ValueError` for unrecognized file formats

## Examples

### Example 1: Basic Loading

```python
# Load the provided example SAE
sae = SAE.load("llama_3_1_8b_layer16_gated_sae.pt")

print(f"Architecture: {sae.architecture}")
print(f"Input dim: {sae.d_in}")
print(f"SAE dim: {sae.d_sae}")

# Use the SAE
input_tensor = torch.randn(10, sae.d_in)
output = sae(input_tensor)
```

### Example 2: Loading EleutherAI SAE

```python
# Load EleutherAI SAE from HuggingFace Hub
sae = SAE.load(
    "EleutherAI/sae-llama-3.1-8b-32x",
    folder_name="layers.23.mlp",
    device="cuda",
    dtype=torch.float16
)

print(f"Loaded {sae.architecture} SAE: {sae.d_in} → {sae.d_sae}")

# Test inference
input_tensor = torch.randn(32, sae.d_in, device="cuda", dtype=torch.float16)
output = sae(input_tensor)
print(f"Sparsity: {output.sparsity:.4f}")
```

### Example 3: Custom Device and Precision

```python
# Load on GPU with half precision
sae = SAE.load(
    "model.pt",
    device="cuda",
    dtype=torch.float16
)

# The SAE is now ready for efficient inference
input_tensor = torch.randn(32, sae.d_in, device="cuda", dtype=torch.float16)
output = sae(input_tensor)
```

### Example 4: Save and Load Cycle

```python
# Create a custom SAE
sae = SAE(
    d_in=768,
    d_sae=3072,
    architecture="gated",
    activation_fn="relu"
)

# Save it
checkpoint = {
    'config': {
        'd_in': sae.d_in,
        'd_sae': sae.d_sae,
        'architecture': sae.architecture,
        'activation_fn': sae.activation_fn_name,
        'normalize_decoder': sae._normalize_decoder,
        'bias_decoder': sae.bias_decoder,
    },
    'state_dict': sae.state_dict()
}
torch.save(checkpoint, "my_sae.pt")

# Load it back
loaded_sae = SAE.load("my_sae.pt")
```

## Dependencies

- **Required**: `torch`, `pathlib`, `json`
- **Optional**: `huggingface_hub` (for HF Hub loading)
- **Optional**: `safetensors` (for .safetensors files)

## Source Type Detection

The loader automatically detects the source type:

- **HuggingFace**: Paths like "username/repo-name"
- **Local files**: Paths ending in `.pt`, `.pth`, `.safetensors`, or absolute/relative file paths

## Performance Notes

- Loading from local files is faster than downloading from HF Hub
- SafeTensors format provides faster loading and better security
- GPU loading with appropriate dtype can significantly speed up inference
- The loader caches HF downloads automatically

## Troubleshooting

### Common Issues

1. **"Cannot infer dimensions"**: The state dict doesn't contain recognizable weight matrices
2. **"Repository not found"**: Invalid HF repo ID or private repository
3. **"safetensors required"**: Install with `pip install safetensors`
4. **"huggingface_hub required"**: Install with `pip install huggingface_hub`

### Debug Tips

- Check file existence with `Path("file.pt").exists()`
- Verify HF repo accessibility in browser
- Use `torch.load()` directly to inspect checkpoint structure
- Enable verbose logging for detailed error information
