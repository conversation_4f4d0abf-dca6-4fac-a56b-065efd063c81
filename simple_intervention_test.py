#!/usr/bin/env python3
"""
Simple test to verify the InterventionResult class works properly.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from itas.analysis.representation_engineer import InterventionResult

def test_intervention_result():
    """Test the InterventionResult class functionality."""
    print("🧪 Testing InterventionResult class...")
    
    # Create test data
    test_results = [
        {
            "input": "Hello, how are you?",
            "original": "Hello, how are you? I'm doing well, thank you!",
            "modified": "Hello, how are you? I'm doing great, thanks for asking!",
        },
        {
            "input": "What is the weather like?",
            "original": "What is the weather like? It's sunny today.",
            "modified": "What is the weather like? It's a beautiful sunny day!",
        }
    ]
    
    original_outputs = [
        {
            "input": "Hello, how are you?",
            "output": "Hello, how are you? I'm doing well, thank you!",
            "tokens": [1, 2, 3, 4, 5],
        },
        {
            "input": "What is the weather like?",
            "output": "What is the weather like? It's sunny today.",
            "tokens": [6, 7, 8, 9, 10],
        }
    ]
    
    modified_outputs = [
        {
            "input": "Hello, how are you?",
            "output": "Hello, how are you? I'm doing great, thanks for asking!",
            "tokens": [1, 2, 3, 4, 11],
        },
        {
            "input": "What is the weather like?",
            "output": "What is the weather like? It's a beautiful sunny day!",
            "tokens": [6, 7, 8, 9, 12],
        }
    ]
    
    # Create InterventionResult
    result = InterventionResult(
        original_output=test_results,
        modified_output=modified_outputs,
        intervention_strength=1.5,
        affected_features=[1, 5, 10, 15],
        metadata={
            "test_texts": ["Hello, how are you?", "What is the weather like?"],
            "generation_kwargs": {"max_new_tokens": 20},
            "original_outputs": original_outputs,
        }
    )
    
    print("✅ InterventionResult created successfully!")
    
    # Test iteration
    print("\n🔍 Testing iteration:")
    print(f"  Length: {len(result)}")
    for i, item in enumerate(result):
        print(f"  Item {i+1}: {type(item)} with keys: {list(item.keys()) if isinstance(item, dict) else 'N/A'}")
    
    # Test indexing
    print("\n🔍 Testing indexing:")
    try:
        first_item = result[0]
        print(f"  First item: {type(first_item)}")
        if isinstance(first_item, dict):
            print(f"    Input: {first_item.get('input', 'N/A')}")
            print(f"    Original: {first_item.get('original', 'N/A')[:30]}...")
            print(f"    Modified: {first_item.get('modified', 'N/A')[:30]}...")
    except Exception as e:
        print(f"  ❌ Indexing failed: {e}")
    
    # Test helper methods
    print("\n🔍 Testing helper methods:")
    print(f"  Is activation comparison: {result.is_activation_comparison()}")
    
    combined_results = result.get_combined_results()
    print(f"  Combined results: {len(combined_results) if combined_results else 0} items")
    
    original_outputs_retrieved = result.get_original_outputs()
    print(f"  Original outputs: {len(original_outputs_retrieved) if original_outputs_retrieved else 0} items")
    
    modified_outputs_retrieved = result.get_modified_outputs()
    print(f"  Modified outputs: {len(modified_outputs_retrieved) if modified_outputs_retrieved else 0} items")
    
    # Test tutorial-style usage
    print("\n🔍 Testing tutorial-style usage:")
    test_prompts = ["Hello, how are you?", "What is the weather like?"]
    
    try:
        for i, (prompt, res) in enumerate(zip(test_prompts, result)):
            print(f"  Prompt {i+1}: {prompt}")
            if isinstance(res, dict):
                print(f"    Original: {res.get('original', 'N/A')[:40]}...")
                print(f"    Modified: {res.get('modified', 'N/A')[:40]}...")
            else:
                print(f"    Unexpected result type: {type(res)}")
        print("  ✅ Tutorial-style usage works!")
    except Exception as e:
        print(f"  ❌ Tutorial-style usage failed: {e}")
    
    return True

def test_activation_comparison_result():
    """Test InterventionResult with activation comparison data."""
    print("\n🧪 Testing InterventionResult with activation comparison...")
    
    # Create activation comparison test data
    test_results = [
        {
            "input": "Hello, how are you?",
            "original": "Original activations (norm: 12.3456)",
            "modified": "Modified activations (norm: 12.4567)",
            "activation_diff_norm": 0.1234,
            "cosine_similarity": 0.9876,
        }
    ]
    
    original_outputs = [
        {
            "input": "Hello, how are you?",
            "output": "Original activations (norm: 12.3456)",
            "norm": 12.3456,
        }
    ]
    
    modified_outputs = [
        {
            "input": "Hello, how are you?",
            "output": "Modified activations (norm: 12.4567)",
            "norm": 12.4567,
            "activation_diff_norm": 0.1234,
            "cosine_similarity": 0.9876,
        }
    ]
    
    # Create InterventionResult for activation comparison
    result = InterventionResult(
        original_output=test_results,
        modified_output=modified_outputs,
        intervention_strength=1.0,
        affected_features=[],
        metadata={
            "test_texts": ["Hello, how are you?"],
            "method": "activation_comparison",
            "note": "Model does not support generation - using activation comparison",
            "original_outputs": original_outputs,
        }
    )
    
    print("✅ Activation comparison InterventionResult created!")
    
    # Test activation comparison detection
    print(f"  Is activation comparison: {result.is_activation_comparison()}")
    
    # Test accessing activation comparison results
    for i, res in enumerate(result):
        print(f"  Result {i+1}:")
        if isinstance(res, dict):
            print(f"    Input: {res.get('input', 'N/A')}")
            print(f"    Original: {res.get('original', 'N/A')}")
            print(f"    Modified: {res.get('modified', 'N/A')}")
            if 'activation_diff_norm' in res:
                print(f"    Activation diff norm: {res['activation_diff_norm']:.4f}")
            if 'cosine_similarity' in res:
                print(f"    Cosine similarity: {res['cosine_similarity']:.4f}")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting InterventionResult tests...\n")
    
    success1 = test_intervention_result()
    success2 = test_activation_comparison_result()
    
    if success1 and success2:
        print("\n🎉 All InterventionResult tests passed!")
    else:
        print("\n💥 Some tests failed.")
